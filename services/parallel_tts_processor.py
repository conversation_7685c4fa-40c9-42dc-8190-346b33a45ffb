import threading
import queue
import time
import logging
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import numpy as np
import io
import sys

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StreamlitLogHandler(logging.Handler):
    """Custom log handler that captures logs for display in Streamlit"""

    def __init__(self):
        super().__init__()
        self.log_buffer = io.StringIO()
        self.logs = []

    def emit(self, record):
        log_entry = self.format(record)
        self.logs.append({
            'timestamp': time.time(),
            'level': record.levelname,
            'message': log_entry,
            'thread': record.thread
        })
        # Keep only last 100 log entries
        if len(self.logs) > 100:
            self.logs = self.logs[-100:]

    def get_logs(self):
        return self.logs.copy()

    def clear_logs(self):
        self.logs.clear()

@dataclass
class TTSTask:
    """Represents a single TTS task to be processed"""
    index: int
    text: str
    kwargs: Dict[str, Any]
    
@dataclass
class TTSResult:
    """Represents the result of a TTS task"""
    index: int
    text: str
    audio_data: np.ndarray
    success: bool
    error: Optional[str] = None
    worker_id: Optional[str] = None
    processing_time: Optional[float] = None

class ParallelTTSProcessor:
    """
    Parallel TTS processor that distributes text chunks across multiple workers
    for concurrent audio generation while maintaining proper order and error handling.
    """

    def __init__(self, tts_service, num_workers: int = 2, progress_callback: Optional[Callable] = None, enable_logging: bool = True):
        """
        Initialize the parallel TTS processor

        Args:
            tts_service: The TTS service instance to use for generation
            num_workers: Number of parallel workers (default: 2)
            progress_callback: Optional callback function for progress updates
            enable_logging: Whether to enable detailed logging (default: True)
        """
        self.tts_service = tts_service
        self.num_workers = num_workers
        self.progress_callback = progress_callback
        self.results_lock = threading.Lock()
        self.enable_logging = enable_logging

        # Set up custom log handler if logging is enabled
        if enable_logging:
            self.log_handler = StreamlitLogHandler()
            self.log_handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            ))
            logger.addHandler(self.log_handler)
        else:
            self.log_handler = None

    def get_logs(self) -> List[Dict[str, Any]]:
        """Get captured logs for display"""
        if self.log_handler:
            return self.log_handler.get_logs()
        return []

    def clear_logs(self):
        """Clear captured logs"""
        if self.log_handler:
            self.log_handler.clear_logs()
        
    def process_segments(self, segments: List[str], **common_kwargs) -> List[TTSResult]:
        """
        Process multiple text segments in parallel
        
        Args:
            segments: List of text segments to process
            **common_kwargs: Common keyword arguments to pass to each TTS call
            
        Returns:
            List[TTSResult]: Results in the same order as input segments
        """
        if not segments:
            return []
            
        logger.info(f"Starting parallel processing of {len(segments)} segments with {self.num_workers} workers")
        
        # Create tasks
        tasks = [TTSTask(index=i, text=segment, kwargs=common_kwargs.copy()) 
                for i, segment in enumerate(segments)]
        
        # Process tasks in parallel
        results = self._process_tasks_parallel(tasks)
        
        # Sort results by index to maintain order
        results.sort(key=lambda x: x.index)
        
        logger.info(f"Completed parallel processing. Success: {sum(1 for r in results if r.success)}/{len(results)}")
        
        return results
    
    def _process_tasks_parallel(self, tasks: List[TTSTask]) -> List[TTSResult]:
        """Process tasks using ThreadPoolExecutor"""
        results = []
        completed_count = 0
        
        with ThreadPoolExecutor(max_workers=self.num_workers, thread_name_prefix="TTSWorker") as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(self._process_single_task, task): task 
                for task in tasks
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                    completed_count += 1
                    
                    # Update progress if callback is provided
                    if self.progress_callback:
                        self.progress_callback(completed_count, len(tasks))
                        
                    logger.info(f"Task {result.index + 1}/{len(tasks)} completed by {result.worker_id} "
                              f"in {result.processing_time:.2f}s")
                              
                except Exception as e:
                    # Create error result
                    error_result = TTSResult(
                        index=task.index,
                        text=task.text,
                        audio_data=np.array([]),
                        success=False,
                        error=str(e),
                        worker_id=threading.current_thread().name
                    )
                    results.append(error_result)
                    completed_count += 1
                    
                    logger.error(f"Task {task.index + 1} failed: {str(e)}")
                    
                    if self.progress_callback:
                        self.progress_callback(completed_count, len(tasks))
        
        return results
    
    def _process_single_task(self, task: TTSTask) -> TTSResult:
        """Process a single TTS task"""
        worker_id = threading.current_thread().name
        start_time = time.time()
        
        logger.info(f"Worker {worker_id} starting task {task.index + 1}: '{task.text[:50]}...'")
        
        try:
            # Generate speech using the TTS service
            audio_data = self.tts_service.generate_speech(text=task.text, **task.kwargs)
            
            processing_time = time.time() - start_time
            
            result = TTSResult(
                index=task.index,
                text=task.text,
                audio_data=audio_data,
                success=True,
                worker_id=worker_id,
                processing_time=processing_time
            )
            
            logger.info(f"Worker {worker_id} completed task {task.index + 1} in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            logger.error(f"Worker {worker_id} failed task {task.index + 1} after {processing_time:.2f}s: {str(e)}")
            
            return TTSResult(
                index=task.index,
                text=task.text,
                audio_data=np.array([]),
                success=False,
                error=str(e),
                worker_id=worker_id,
                processing_time=processing_time
            )

    def process_segments_with_retry(self, segments: List[str], max_retries: int = 2, **common_kwargs) -> List[TTSResult]:
        """
        Process segments with automatic retry for failed tasks

        Args:
            segments: List of text segments to process
            max_retries: Maximum number of retries for failed tasks
            **common_kwargs: Common keyword arguments to pass to each TTS call

        Returns:
            List[TTSResult]: Results in the same order as input segments
        """
        results = self.process_segments(segments, **common_kwargs)

        # Retry failed tasks
        for retry_attempt in range(max_retries):
            failed_indices = [i for i, result in enumerate(results) if not result.success]

            if not failed_indices:
                break  # All tasks succeeded

            logger.info(f"Retry attempt {retry_attempt + 1}/{max_retries} for {len(failed_indices)} failed tasks")

            # Create retry tasks
            retry_segments = [segments[i] for i in failed_indices]
            retry_results = self.process_segments(retry_segments, **common_kwargs)

            # Update results with retry results
            for i, retry_result in enumerate(retry_results):
                original_index = failed_indices[i]
                if retry_result.success:
                    results[original_index] = retry_result
                    logger.info(f"Task {original_index + 1} succeeded on retry {retry_attempt + 1}")

        return results

    def get_processing_stats(self, results: List[TTSResult]) -> Dict[str, Any]:
        """
        Get processing statistics from results

        Args:
            results: List of TTS results

        Returns:
            Dict containing processing statistics
        """
        successful_results = [r for r in results if r.success]
        failed_results = [r for r in results if not r.success]

        stats = {
            'total_tasks': len(results),
            'successful_tasks': len(successful_results),
            'failed_tasks': len(failed_results),
            'success_rate': len(successful_results) / len(results) if results else 0,
            'total_processing_time': sum(r.processing_time or 0 for r in results),
            'average_processing_time': sum(r.processing_time or 0 for r in successful_results) / len(successful_results) if successful_results else 0,
            'worker_distribution': {}
        }

        # Calculate worker distribution
        for result in successful_results:
            worker_id = result.worker_id or 'unknown'
            if worker_id not in stats['worker_distribution']:
                stats['worker_distribution'][worker_id] = 0
            stats['worker_distribution'][worker_id] += 1

        # Add error summary
        if failed_results:
            error_summary = {}
            for result in failed_results:
                error = result.error or 'Unknown error'
                if error not in error_summary:
                    error_summary[error] = 0
                error_summary[error] += 1
            stats['error_summary'] = error_summary

        return stats

    def get_worker_status(self) -> Dict[str, Any]:
        """
        Get current worker status and performance metrics

        Returns:
            Dict containing worker status information
        """
        logs = self.get_logs()

        # Analyze recent logs to determine worker status
        recent_logs = [log for log in logs if time.time() - log['timestamp'] < 60]  # Last minute

        worker_activity = {}
        for log in recent_logs:
            if 'Worker' in log['message']:
                # Extract worker ID from message
                parts = log['message'].split()
                for i, part in enumerate(parts):
                    if part == 'Worker' and i + 1 < len(parts):
                        worker_id = parts[i + 1]
                        if worker_id not in worker_activity:
                            worker_activity[worker_id] = {
                                'last_activity': log['timestamp'],
                                'status': 'unknown',
                                'tasks_completed': 0,
                                'tasks_failed': 0
                            }

                        worker_activity[worker_id]['last_activity'] = max(
                            worker_activity[worker_id]['last_activity'],
                            log['timestamp']
                        )

                        if 'completed' in log['message']:
                            worker_activity[worker_id]['tasks_completed'] += 1
                            worker_activity[worker_id]['status'] = 'completed_task'
                        elif 'failed' in log['message']:
                            worker_activity[worker_id]['tasks_failed'] += 1
                            worker_activity[worker_id]['status'] = 'failed_task'
                        elif 'starting' in log['message']:
                            worker_activity[worker_id]['status'] = 'processing'

        return {
            'total_workers': self.num_workers,
            'worker_activity': worker_activity,
            'recent_log_count': len(recent_logs),
            'total_log_count': len(logs)
        }

    def process_segments_queue_based(self, segments: List[str], **common_kwargs) -> List[TTSResult]:
        """
        Process segments using a queue-based approach where workers pick up tasks dynamically

        Args:
            segments: List of text segments to process
            **common_kwargs: Common keyword arguments to pass to each TTS call

        Returns:
            List[TTSResult]: Results in the same order as input segments
        """
        if not segments:
            return []

        logger.info(f"Starting queue-based processing of {len(segments)} segments with {self.num_workers} workers")

        # Create task queue
        task_queue = queue.Queue()
        result_queue = queue.Queue()

        # Add all tasks to the queue
        for i, segment in enumerate(segments):
            task = TTSTask(index=i, text=segment, kwargs=common_kwargs.copy())
            task_queue.put(task)

        # Create worker threads
        workers = []
        for worker_id in range(self.num_workers):
            worker = threading.Thread(
                target=self._queue_worker,
                args=(task_queue, result_queue, f"QueueWorker-{worker_id + 1}"),
                daemon=True
            )
            workers.append(worker)
            worker.start()

        # Collect results
        results = []
        completed_count = 0

        while completed_count < len(segments):
            try:
                result = result_queue.get(timeout=300)  # 5 minute timeout
                results.append(result)
                completed_count += 1

                # Update progress if callback is provided
                if self.progress_callback:
                    self.progress_callback(completed_count, len(segments))

                logger.info(f"Task {result.index + 1}/{len(segments)} completed by {result.worker_id} "
                          f"in {result.processing_time:.2f}s")

            except queue.Empty:
                logger.error("Timeout waiting for results from workers")
                break

        # Wait for all workers to finish
        for _ in range(self.num_workers):
            task_queue.put(None)  # Sentinel to stop workers

        for worker in workers:
            worker.join(timeout=10)

        # Sort results by index to maintain order
        results.sort(key=lambda x: x.index)

        logger.info(f"Completed queue-based processing. Success: {sum(1 for r in results if r.success)}/{len(results)}")

        return results

    def _queue_worker(self, task_queue: queue.Queue, result_queue: queue.Queue, worker_id: str):
        """Worker function for queue-based processing"""
        logger.info(f"Worker {worker_id} started")

        while True:
            try:
                task = task_queue.get(timeout=1)

                if task is None:  # Sentinel to stop worker
                    logger.info(f"Worker {worker_id} stopping")
                    break

                # Process the task
                start_time = time.time()
                logger.info(f"Worker {worker_id} starting task {task.index + 1}: '{task.text[:50]}...'")

                try:
                    # Generate speech using the TTS service
                    audio_data = self.tts_service.generate_speech(text=task.text, **task.kwargs)

                    processing_time = time.time() - start_time

                    result = TTSResult(
                        index=task.index,
                        text=task.text,
                        audio_data=audio_data,
                        success=True,
                        worker_id=worker_id,
                        processing_time=processing_time
                    )

                    logger.info(f"Worker {worker_id} completed task {task.index + 1} in {processing_time:.2f}s")

                except Exception as e:
                    processing_time = time.time() - start_time

                    logger.error(f"Worker {worker_id} failed task {task.index + 1} after {processing_time:.2f}s: {str(e)}")

                    result = TTSResult(
                        index=task.index,
                        text=task.text,
                        audio_data=np.array([]),
                        success=False,
                        error=str(e),
                        worker_id=worker_id,
                        processing_time=processing_time
                    )

                result_queue.put(result)
                task_queue.task_done()

            except queue.Empty:
                continue  # No task available, keep waiting
            except Exception as e:
                logger.error(f"Worker {worker_id} encountered unexpected error: {str(e)}")
                break

        logger.info(f"Worker {worker_id} finished")
