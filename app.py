from pathlib import Path
import datetime
import os
import glob
import tempfile
import json
import time
import hashlib
import io
import numpy as np
import soundfile as sf
import streamlit as st

from utils.sentence_splitter import split_text_into_sentences
from services.tts_factory import TTSFactory
from services.tts_manager import TTSManager
from services.parallel_tts_processor import ParallelTTSProcessor

def main():
    st.set_page_config(page_title="EchoMind TTS", layout="wide")
    
    st.title("EchoMind Text-to-Speech Generator")
    
    # Sidebar for configuration
    with st.sidebar:
        st.title("EchoMind")
        st.markdown("---")
        
        # Create tabs for better organization
        setup_tab, advanced_tab = st.tabs(["Setup", "Advanced"])
        
        with setup_tab:
            # Basic setup section
            st.subheader("TTS Configuration")
            
            # TTS service selection with more descriptive options
            service_options = {
                "spark": "Spark TTS",
                "cosyvoice": "CosyVoice (Local)",
                "cosyvoice_api": "CosyVoice API"
            }
            
            # Load previous service type from session state if available
            previous_service_type = st.session_state.get('saved_service_type')
            service_type = st.selectbox(
                "TTS Engine", 
                options=list(service_options.keys()),
                format_func=lambda x: service_options[x],
                index=list(service_options.keys()).index(previous_service_type) if previous_service_type else 0
            )
            
            # Store configuration in session state
            
            config = {}
            
            # Show relevant controls based on service selection
            if service_type == "cosyvoice_api":
                # CosyVoice API specific settings in an expandable section
                with st.expander("CosyVoice API Settings", expanded=True):
                    config['api_url'] = api_url = st.text_input("API URL", 
                        value=st.session_state.get('saved_api_url', "http://localhost:8000"))
                    config['speed'] = speed = st.slider("Speech Speed", 
                        min_value=0.5, max_value=1.5, 
                        value=st.session_state.get('saved_speed', 0.8), 
                        step=0.1)
                    config['prompt_text'] = prompt_text = st.text_area("Prompt Text",
                        value=st.session_state.get('saved_prompt_text', "You may have a billion dollars..."),
                        height=80)
            
            elif service_type == "cosyvoice":
                with st.expander("CosyVoice Settings", expanded=True):
                    config['speaker_id'] = speaker_id = st.text_input("Speaker ID",
                        value=st.session_state.get('saved_speaker_id', ""))
            
            # Device selection
            device_map = {"cpu": "CPU", "cuda": "NVIDIA GPU", "mps": "Apple Silicon"}
            device_options = ["cpu", "cuda", "mps"]
            default_device = st.session_state.get('saved_device', "mps" if "mps" in device_options else "cpu")
            
            config['device'] = device = st.selectbox(
                "Processing Device", 
                options=device_options,
                format_func=lambda x: device_map.get(x, x),
                index=device_options.index(default_device)
            )
            
            # Save all configuration
            for key, value in config.items():
                st.session_state[f'saved_{key}'] = value
            st.session_state['saved_service_type'] = service_type
            
            # Initialize button with better styling
            initialize_button = st.button("Initialize TTS Service", type="primary", use_container_width=True)
            
            # Auto-initialize if configuration exists but service is not initialized
            if 'tts_service' not in st.session_state and 'saved_service_type' in st.session_state:
                initialize_button = True
            
            # Handle the initialize button logic
            if initialize_button:
                with st.spinner("Initializing TTS service..."):
                    try:
                        # Use TTSManager to get or create service
                        if service_type == "cosyvoice_api":
                            service_config = {
                                'api_url': config['api_url'],
                                'speed': config['speed']
                            }
                        else:
                            service_config = {
                                'device': config['device'],
                                'speaker_id': config.get('speaker_id')
                            }
                        
                        st.session_state['tts_service'] = TTSManager.get_service(
                            service_type,
                            **service_config
                        )
                        
                        st.toast(f"{service_options.get(service_type, service_type)} initialized successfully!", icon="✅")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Error initializing TTS service: {str(e)}")
        
            # Store service type in session state for later use
            st.session_state['service_type'] = service_type
            
            # Minimum words settings
            st.markdown("---")
            st.subheader("Text Segmentation")
            min_words = st.slider(
                "Minimum Words per Segment", 
                min_value=10, 
                max_value=100, 
                value=30,
                help="Controls how text is split into segments"
            )
            max_words = st.slider(
                "Maximum Words per Segment", 
                min_value=15,
                max_value=100,
                value=80,
                help="Controls how text is split into segments"
            )
        
        with advanced_tab:
            st.subheader("Processing Options")
            
            # Additional advanced options could go here
            volume_boost = st.slider(
                "Volume Boost (dB)", 
                min_value=0.0, 
                max_value=10.0, 
                value=5.0, 
                step=0.5,
                help="Increase the volume of the output audio"
            )
            
            # Add silence options
            silence_duration = st.slider(
                "Silence Between Segments (sec)",
                min_value=0.5,
                max_value=2.0,
                value=1.0,
                step=0.1,
                help="Duration of silence between segments"
            )

            # Parallel processing options
            st.subheader("Parallel Processing")
            enable_parallel = st.checkbox(
                "Enable Parallel Processing",
                value=True,
                help="Process multiple text segments concurrently to improve speed"
            )

            num_workers = st.slider(
                "Number of Workers",
                min_value=1,
                max_value=8,
                value=2,
                step=1,
                disabled=not enable_parallel,
                help="Number of parallel workers for audio generation (more workers = faster processing but higher resource usage)"
            )

            processing_method = st.selectbox(
                "Processing Method",
                options=["threadpool", "queue"],
                index=0,
                disabled=not enable_parallel,
                format_func=lambda x: "ThreadPool (Batch)" if x == "threadpool" else "Queue (Dynamic)",
                help="ThreadPool processes all segments at once, Queue allows dynamic work distribution"
            )

            enable_retry = st.checkbox(
                "Enable Automatic Retry",
                value=True,
                disabled=not enable_parallel,
                help="Automatically retry failed segments up to 2 times"
            )
            
            # Cache settings
            st.subheader("Cache Management")
            st.checkbox("Auto-reload from cache when available", value=True, 
                       help="Automatically load audio from cache if it exists for the current text")
            
            if st.button("Clear All Caches", type="secondary"):
                if 'clear_cache_confirmed' not in st.session_state:
                    st.session_state['clear_cache_confirmed'] = False
                    st.warning("This will delete all cached audio files. Are you sure?")
                    st.button("Yes, clear all caches", key="confirm_clear", type="primary")
                elif st.session_state['clear_cache_confirmed']:
                    # Code to clear caches would go here
                    st.success("All caches cleared successfully!")
                    st.session_state['clear_cache_confirmed'] = False
        
        # Processing status section at the bottom of sidebar
        st.markdown("---")
        if 'tts_service' in st.session_state:
            st.success(f"✓ {service_options.get(service_type, service_type)} initialized")
        else:
            st.warning("TTS service not initialized", icon="ℹ️")
        
        # Version info
        st.markdown("<div style='position: fixed; bottom: 20px; text-align: center; width: inherit;'><p style='color: #888; font-size: 0.8em;'>EchoMind v1.0.0</p></div>", unsafe_allow_html=True)
        
    # Store these values in session state for other parts of the app to access
    st.session_state['volume_boost'] = volume_boost if 'advanced_tab' in locals() else 5.0
    st.session_state['silence_duration'] = silence_duration if 'advanced_tab' in locals() else 1.0
    st.session_state['enable_parallel'] = enable_parallel if 'advanced_tab' in locals() else True
    st.session_state['num_workers'] = num_workers if 'advanced_tab' in locals() else 2
    st.session_state['processing_method'] = processing_method if 'advanced_tab' in locals() else "threadpool"
    st.session_state['enable_retry'] = enable_retry if 'advanced_tab' in locals() else True
    
    # Main content area
    col1, col2 = st.columns([2, 3])
    
    with col1:
        st.header("Input")
        
        # File uploader for text input
        uploaded_text_file = st.file_uploader("Upload Text File", type=["txt"])
        
        # Text content (either uploaded or input directly)
        text_content = ""
        if uploaded_text_file is not None:
            text_content = uploaded_text_file.read().decode("utf-8")
        
        # Direct text input as alternative
        if not text_content:
            text_content = st.text_area("Or enter text directly", height=300, 
                                      placeholder="Enter or paste your text here...")
        
        # Audio prompt upload
        uploaded_prompt = st.file_uploader("Upload Voice Prompt", type=["wav", "mp3"])
        
        default_prompt_path = "./assets/prompt_audio.wav"
        prompt_path = default_prompt_path
        
        if uploaded_prompt:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
                tmp_file.write(uploaded_prompt.read())
                prompt_path = tmp_file.name
        
        # Check if default prompt exists
        if not uploaded_prompt and not os.path.exists(default_prompt_path):
            st.warning(f"Default prompt file not found at {default_prompt_path}")
        
        # Buttons for splitting text and loading cache
        split_col, cache_col = st.columns(2)
        
        with split_col:
            if st.button("Split Text into Segments"):
                if text_content:
                    with st.spinner("Splitting text..."):
                        segments = split_text_into_sentences(text_content, min_words=min_words, max_words=max_words)
                        st.session_state['segments'] = segments
                        st.session_state['edited_segments'] = "\n\n".join(segments)
                        st.session_state['original_text'] = text_content
                else:
                    st.warning("Please provide text content first")
        
        with cache_col:
            if st.button("Reload from Cache"):
                if text_content:
                    # Try to find cache for the current text
                    cache_path = find_cache_for_text(text_content)
                    if cache_path:
                        with st.spinner(f"Loading from cache: {os.path.basename(cache_path)}"):
                            # Load segments and audio from cache
                            segments, segment_audios = load_segments_from_cache(cache_path)
                            
                            # Update session state
                            st.session_state['segments'] = segments
                            st.session_state['edited_segments'] = "\n\n".join(segments)
                            st.session_state['segment_audios'] = segment_audios
                            st.session_state['cache_path'] = cache_path
                            
                            # Check for merged audio in the cache directory first
                            cache_merged_path = os.path.join(cache_path, "merged.wav")
                            if os.path.exists(cache_merged_path):
                                # Load merged audio directly from cache
                                merged_audio, sample_rate = sf.read(cache_merged_path)
                                st.session_state['merged_audio'] = merged_audio
                                st.session_state['merged_audio_path'] = cache_merged_path
                            else:
                                # If no merged audio in cache, merge the segments
                                # Load and merge all audios if segment_audios is not empty
                                if segment_audios:
                                    # Merge audio segments
                                    wavs = []
                                    
                                    # Get sample rate from the first segment's audio
                                    if len(segment_audios) > 0 and 'sample_rate' in segment_audios[0]:
                                        sample_rate = segment_audios[0]['sample_rate']
                                    else:
                                        # Fallback to service-specific defaults
                                        service_type = st.session_state.get('service_type', 'spark')
                                        sample_rate = 24000 if service_type.startswith("cosyvoice") else 16000
                                    
                                    # Load room tone
                                    room_tone_path = "./assets/room_tone_24k.wav" if service_type.startswith("cosyvoice") else "./assets/room_tone_16k.wav"
                                    if os.path.exists(room_tone_path) and silence_duration == 1.0:
                                        room_tone, _ = sf.read(room_tone_path)
                                        # Convert to mono if stereo
                                        if len(room_tone.shape) > 1 and room_tone.shape[1] > 1:
                                            room_tone = np.mean(room_tone, axis=1)
                                    else:
                                        room_tone = np.zeros(int(sample_rate*silence_duration))
                                    
                                    for i, segment_data in enumerate(segment_audios):
                                        wavs.append(segment_data['audio'])
                                        
                                        # Add a room tone gap after each segment except the last
                                        if i < len(segment_audios) - 1:
                                            wavs.append(room_tone)
                                    
                                    # Merge all segments
                                    merged_audio = np.concatenate(wavs, axis=0)
                                    merged_audio = increase_volume(merged_audio, gain_db=st.session_state.get('volume_boost', 5.0)) # Apply volume boost
                                    st.session_state['merged_audio'] = merged_audio
                                    
                                    # Save merged audio to cache directory
                                    cache_merged_path = os.path.join(cache_path, "merged.wav")
                                    
                                    # Determine the sample rate from the segment audios if available
                                    if len(st.session_state['segment_audios']) > 0 and 'sample_rate' in st.session_state['segment_audios'][0]:
                                        sample_rate = st.session_state['segment_audios'][0]['sample_rate']
                                    else:
                                        # Fallback to service-specific defaults
                                        service_type = st.session_state.get('service_type', 'spark')
                                        sample_rate = 24000 if service_type.startswith("cosyvoice") else 16000
                                        
                                    sf.write(cache_merged_path, merged_audio, sample_rate)
                                    st.session_state['merged_audio_path'] = cache_merged_path
                            
                            st.success(f"Successfully loaded {len(segments)} segments from cache!")
                            st.rerun()
                    else:
                        st.warning("No cache found for this text. Generate speech first.")
                else:
                    st.warning("Please provide text content first")
                
        # Upload segment audio files
        st.markdown("---")
        st.subheader("Upload Existing Segment Audios")
        upload_segments = st.file_uploader("Upload segment WAV files (must match the number of text segments)", 
                                          type=["wav"], accept_multiple_files=True)
        
        if upload_segments and 'edited_segments' in st.session_state:
            segments = [seg.strip() for seg in st.session_state['edited_segments'].split("\n\n") if seg.strip()]
            if len(upload_segments) == len(segments):
                if st.button("Load Uploaded Segments"):
                    with st.spinner("Loading audio segments..."):
                        # Create a cache directory for these uploaded segments
                        if 'original_text' in st.session_state:
                            original_text = st.session_state['original_text']
                        else:
                            original_text = text_content
                            
                        cache_dir_name = get_cache_dir_name(original_text)
                        cache_path = os.path.join("output", cache_dir_name)
                        os.makedirs(cache_path, exist_ok=True)
                        st.session_state['cache_path'] = cache_path
                        
                        st.session_state['segment_audios'] = []
                        for i, (segment_file, segment_text) in enumerate(zip(upload_segments, segments)):
                            # Format number with leading zeros
                            padded_index = f"{i+1:03d}"
                            
                            # Save the uploaded file directly to cache
                            segment_file_path = os.path.join(cache_path, f"segment_{padded_index}.wav")
                            with open(segment_file_path, "wb") as f:
                                f.write(segment_file.read())
                            
                            # Read audio data
                            audio_data, sample_rate = sf.read(segment_file_path)
                            
                            # Store segment data
                            st.session_state['segment_audios'].append({
                                'index': i,
                                'text': segment_text,
                                'audio': audio_data,
                                'file_path': segment_file_path,
                                'sample_rate': sample_rate  # Store actual sample rate
                            })
                        
                        # Save text segments to cache
                        with open(os.path.join(cache_path, "segments.txt"), "w", encoding="utf-8") as f:
                            f.write("\n\n".join(segments))
                        
                        # Save original text to cache
                        with open(os.path.join(cache_path, "original_text.txt"), "w", encoding="utf-8") as f:
                            f.write(original_text)
                            
                        # Save metadata
                        metadata = {
                            "timestamp": time.time(),
                            "segment_count": len(segments),
                            "text_hash": get_text_hash(original_text)
                        }
                        
                        with open(os.path.join(cache_path, "metadata.json"), "w", encoding="utf-8") as f:
                            json.dump(metadata, f)
                        
                        # Now merge the audio segments as we do after generation
                        wavs = []
                        
                        # Get sample rate from the first segment's audio if available
                        if len(st.session_state['segment_audios']) > 0 and 'sample_rate' in st.session_state['segment_audios'][0]:
                            sample_rate = st.session_state['segment_audios'][0]['sample_rate']
                        else:
                            # Fallback to service-specific defaults
                            service_type = st.session_state.get('service_type', 'spark')
                            sample_rate = 24000 if service_type.startswith("cosyvoice") else 16000
                        
                        # Load room tone
                        room_tone_path = "./assets/room_tone_24k.wav" if service_type.startswith("cosyvoice") else "./assets/room_tone_16k.wav"
                        if os.path.exists(room_tone_path) and silence_duration == 1.0:
                            room_tone, _ = sf.read(room_tone_path)
                            # Convert to mono if stereo
                            if len(room_tone.shape) > 1 and room_tone.shape[1] > 1:
                                room_tone = np.mean(room_tone, axis=1)
                        else:
                            room_tone = np.zeros(int(sample_rate*silence_duration))
                            
                        for i, segment_data in enumerate(st.session_state['segment_audios']):
                            wavs.append(segment_data['audio'])
                            
                            # Add a room tone gap after each segment except the last
                            if i < len(st.session_state['segment_audios']) - 1:
                                wavs.append(room_tone)
                        
                        # Merge all segments
                        merged_audio = np.concatenate(wavs, axis=0)
                        merged_audio = increase_volume(merged_audio, gain_db=st.session_state.get('volume_boost', 5.0))
                        st.session_state['merged_audio'] = merged_audio
                        
                        # Save merged audio to cache directory instead of output root
                        cache_merged_path = os.path.join(cache_path, "merged.wav")
                        sf.write(cache_merged_path, merged_audio, sample_rate)
                        st.session_state['merged_audio_path'] = cache_merged_path
                        
                        st.success("Audio segments loaded and merged successfully!")
                        st.rerun()
            else:
                st.warning(f"Number of uploaded files ({len(upload_segments)}) does not match number of text segments ({len(segments)})")
    
    with col2:
        st.header("Text Processing")
        
        # Display and allow editing of segments
        if 'edited_segments' in st.session_state:
            st.session_state['edited_segments'] = st.text_area(
                "Edit Segments (one segment per paragraph)",
                st.session_state['edited_segments'],
                height=400
            )
        
        # Generate button
        generate_col, remerge_col = st.columns(2)
        
        with generate_col:
            generate_button = st.button("Generate Speech", type="primary")
        
        with remerge_col:
            remerge_button = st.button("Remerge Audio Segments")
        

        # load the room tone wav from the assets folder
        room_tone_path = "./assets/room_tone_24k.wav" if service_type.startswith("cosyvoice") else "./assets/room_tone_16k.wav"
        if os.path.exists(room_tone_path):
            room_tone, sample_rate = sf.read(room_tone_path)
            # Convert to mono if stereo
            if len(room_tone.shape) > 1 and room_tone.shape[1] > 1:
                room_tone = np.mean(room_tone, axis=1)
        else:
            st.error(f"Room tone file not found at {room_tone_path}")
            return
        
        # Process for generation
        if generate_button:
            if 'edited_segments' in st.session_state and 'tts_service' in st.session_state:
                # Split by double newlines to get segments
                segments = [seg.strip() for seg in st.session_state['edited_segments'].split("\n\n") if seg.strip()]
                
                if segments:
                    st.session_state['final_segments'] = segments
                    st.session_state['segment_audios'] = []
                    st.session_state['merged_audio'] = None
                    
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    # Check if parallel processing is enabled
                    enable_parallel = st.session_state.get('enable_parallel', True)

                    if enable_parallel and len(segments) > 1:
                        # Use parallel processing
                        num_workers = st.session_state.get('num_workers', 2)
                        processing_method = st.session_state.get('processing_method', 'threadpool')
                        enable_retry = st.session_state.get('enable_retry', True)

                        status_text.text(f"Starting parallel processing with {num_workers} workers...")

                        # Create progress callback
                        def progress_callback(completed, total):
                            progress_bar.progress(completed / total)
                            status_text.text(f"Processing segments: {completed}/{total} completed")

                        # Create parallel processor
                        processor = ParallelTTSProcessor(
                            tts_service=st.session_state['tts_service'],
                            num_workers=num_workers,
                            progress_callback=progress_callback,
                            enable_logging=True
                        )

                        # Store processor in session state for monitoring
                        st.session_state['current_processor'] = processor

                        # Prepare common kwargs
                        common_kwargs = {'prompt_speech_path': prompt_path}

                        # Handle CosyVoice API prompt text (only for first segment)
                        if service_type == "cosyvoice_api" and prompt_text:
                            # For parallel processing, we need to handle prompt_text differently
                            # We'll process the first segment separately if needed
                            pass

                        try:
                            # Process segments in parallel
                            if processing_method == "queue":
                                results = processor.process_segments_queue_based(segments, **common_kwargs)
                            else:
                                if enable_retry:
                                    results = processor.process_segments_with_retry(segments, max_retries=2, **common_kwargs)
                                else:
                                    results = processor.process_segments(segments, **common_kwargs)

                            # Convert results to the expected format and save to cache
                            st.session_state['segment_audios'] = []

                            # Create cache directory
                            if 'original_text' in st.session_state:
                                original_text = st.session_state['original_text']
                            else:
                                original_text = text_content

                            cache_dir_name = get_cache_dir_name(original_text)
                            cache_path = os.path.join("output", cache_dir_name)
                            os.makedirs(cache_path, exist_ok=True)
                            st.session_state['cache_path'] = cache_path

                            # Determine sample rate based on service type
                            service_type = st.session_state.get('service_type', 'spark')
                            sample_rate = 24000 if service_type.startswith("cosyvoice") else 16000

                            # Process results and save to cache
                            for result in results:
                                if result.success:
                                    # Save segment audio to cache
                                    padded_index = f"{result.index+1:03d}"
                                    segment_file = os.path.join(cache_path, f"segment_{padded_index}.wav")
                                    sf.write(segment_file, result.audio_data, sample_rate)

                                    st.session_state['segment_audios'].append({
                                        'index': result.index,
                                        'text': result.text,
                                        'audio': result.audio_data,
                                        'file_path': segment_file,
                                        'sample_rate': sample_rate
                                    })
                                else:
                                    st.error(f"Error generating segment {result.index+1}: {result.error}")

                            # Display processing statistics
                            stats = processor.get_processing_stats(results)
                            status_text.text(f"Parallel processing completed! Success rate: {stats['success_rate']:.1%}, "
                                           f"Average time per segment: {stats['average_processing_time']:.2f}s")

                        except Exception as e:
                            st.error(f"Error in parallel processing: {str(e)}")
                            status_text.text("Falling back to sequential processing...")
                            enable_parallel = False  # Fall back to sequential

                    if not enable_parallel or len(segments) == 1:
                        # Use sequential processing (original code)
                        for i, seg in enumerate(segments):
                            status_text.text(f"Generating segment {i+1} of {len(segments)}...")

                            try:
                                # Generate audio for segment
                                kwargs = {
                                    'prompt_speech_path': prompt_path,
                                }

                                # If using CosyVoice API, add prompt text
                                if service_type == "cosyvoice_api" and prompt_text and i == 0:
                                    # Only pass prompt_text with the first segment
                                    kwargs['prompt_text'] = prompt_text

                                # Generate speech
                                wav_data = st.session_state['tts_service'].generate_speech(
                                    text=seg,
                                    **kwargs
                                )

                                # Determine sample rate based on service type
                                service_type = st.session_state.get('service_type', 'spark')
                                sample_rate = 24000 if service_type.startswith("cosyvoice") else 16000

                                # Create cache directory if needed
                                if 'original_text' in st.session_state:
                                    original_text = st.session_state['original_text']
                                else:
                                    original_text = text_content

                                cache_dir_name = get_cache_dir_name(original_text)
                                cache_path = os.path.join("output", cache_dir_name)
                                os.makedirs(cache_path, exist_ok=True)
                                st.session_state['cache_path'] = cache_path

                                # Save segment audio directly to cache
                                padded_index = f"{i+1:03d}"
                                segment_file = os.path.join(cache_path, f"segment_{padded_index}.wav")
                                sf.write(segment_file, wav_data, sample_rate)

                                st.session_state['segment_audios'].append({
                                    'index': i,
                                    'text': seg,
                                    'audio': wav_data,
                                    'file_path': segment_file,
                                    'sample_rate': sample_rate
                                })

                                progress_bar.progress((i + 1) / len(segments))

                            except Exception as e:
                                st.error(f"Error generating segment {i+1}: {str(e)}")
                                break
                    
                    # Merge all segments
                    if len(st.session_state['segment_audios']) == len(segments):
                        status_text.text("Merging audio segments...")
                        wavs = []
                        
                        # Get sample rate from the first segment's audio
                        if len(st.session_state['segment_audios']) > 0 and 'sample_rate' in st.session_state['segment_audios'][0]:
                            sample_rate = st.session_state['segment_audios'][0]['sample_rate']
                        else:
                            # Fallback to service-specific defaults
                            service_type = st.session_state.get('service_type', 'spark')
                            sample_rate = 24000 if service_type.startswith("cosyvoice") else 16000
                        
                        for i, segment_data in enumerate(st.session_state['segment_audios']):
                            wavs.append(segment_data['audio'])
                            
                            # Add a room tone gap after each segment except the last
                            if i < len(st.session_state['segment_audios']) - 1:
                                if silence_duration == 1.0:
                                    wavs.append(room_tone)
                                else:
                                    silence_gap = np.zeros(int(sample_rate*silence_duration))
                                    wavs.append(silence_gap)
                        
                        # Merge all segments
                        merged_audio = np.concatenate(wavs, axis=0)
                        merged_audio = increase_volume(merged_audio, gain_db=st.session_state.get('volume_boost', 5.0))
                        st.session_state['merged_audio'] = merged_audio
                        
                        # Save merged audio directly to cache directory
                        cache_merged_path = os.path.join(cache_path, "merged.wav")
                        sf.write(cache_merged_path, merged_audio, sample_rate)
                        st.session_state['merged_audio_path'] = cache_merged_path
                        
                        # Save metadata file
                        metadata = {
                            "timestamp": time.time(),
                            "segment_count": len(segments),
                            "text_hash": get_text_hash(original_text)
                        }
                        metadata_path = os.path.join(cache_path, "metadata.json")
                        with open(metadata_path, "w", encoding="utf-8") as f:
                            json.dump(metadata, f)
                        
                        # Save original text file if not already saved
                        original_text_path = os.path.join(cache_path, "original_text.txt")
                        if not os.path.exists(original_text_path):
                            with open(original_text_path, "w", encoding="utf-8") as f:
                                f.write(original_text)
                            
                        # Make sure segments.txt is updated with current segments
                        segments_path = os.path.join(cache_path, "segments.txt")
                        with open(segments_path, "w", encoding="utf-8") as f:
                            f.write("\n\n".join(segments))
                            
                        status_text.text(f"Audio cached for future use in {os.path.basename(cache_path)}")
                    else:
                        status_text.text("No audio segments to merge")
                    
                    status_text.text("Generation complete!")
                else:
                    st.warning("No segments to process")
            else:
                if 'tts_service' not in st.session_state:
                    st.error("Please initialize TTS service first")
                else:
                    st.warning("Please split the text first")
        
        # Process for remerge
        if remerge_button and 'segment_audios' in st.session_state and st.session_state['segment_audios']:
            with st.spinner("Remerging audio segments..."):
                wavs = []
                
                # Use correct sample rate from the segments if available
                if len(st.session_state['segment_audios']) > 0 and 'sample_rate' in st.session_state['segment_audios'][0]:
                    sample_rate = st.session_state['segment_audios'][0]['sample_rate']
                else:
                    # Fallback to service-specific defaults
                    service_type = st.session_state.get('service_type', 'spark')
                    sample_rate = 24000 if service_type.startswith("cosyvoice") else 16000
                
                for i, segment_data in enumerate(st.session_state['segment_audios']):
                    # Add segment audio to the list
                    wavs.append(segment_data['audio'])
                    
                    # Add a room tone gap after each segment except the last
                    if i < len(st.session_state['segment_audios']) - 1:
                        if silence_duration == 1.0:
                            wavs.append(room_tone)
                        else:
                            silence_gap = np.zeros(int(sample_rate*silence_duration))
                            wavs.append(silence_gap)
                
                # Merge all segments
                merged_audio = np.concatenate(wavs, axis=0)
                merged_audio = increase_volume(merged_audio, gain_db=st.session_state.get('volume_boost', 5.0))
                st.session_state['merged_audio'] = merged_audio
                
                # We should always have a cache_path in session_state when we have segment_audios
                if 'cache_path' in st.session_state:
                    cache_merged_path = os.path.join(st.session_state['cache_path'], "merged.wav")
                    sf.write(cache_merged_path, merged_audio, sample_rate)
                    st.session_state['merged_audio_path'] = cache_merged_path
                    st.toast(f"Audio remerged and saved to {cache_merged_path}", icon="✅")
                else:
                    # This is a fallback that should rarely happen
                    st.warning("Warning: No cache path found. Creating a new one.")
                    if 'original_text' in st.session_state:
                        original_text = st.session_state['original_text']
                    else:
                        original_text = "remerged_audio"
                        
                    cache_dir_name = get_cache_dir_name(original_text)
                    cache_path = os.path.join("output", cache_dir_name)
                    os.makedirs(cache_path, exist_ok=True)
                    st.session_state['cache_path'] = cache_path
                    
                    cache_merged_path = os.path.join(cache_path, "merged.wav")
                    sf.write(cache_merged_path, merged_audio, sample_rate)
                    st.session_state['merged_audio_path'] = cache_merged_path
                    st.toast(f"Audio remerged and saved to {cache_merged_path}", icon="✅")
    
    # Display results section if we have generated segments
    if 'segment_audios' in st.session_state and st.session_state['segment_audios']:
        st.header("Results")

        # Display each segment with its audio and regenerate button
        for i, segment_data in enumerate(st.session_state['segment_audios']):
            st.markdown(f"**Segment {i+1}**")
            with st.container():
                col_text, col_audio, col_button = st.columns([3, 2, 1])
                
                with col_text:
                    segment_key = f"segment_text_{i}"
                    segment_text = st.text_area(f"Segment {i+1} Text", segment_data['text'], height=100, key=segment_key, label_visibility="collapsed")
                    # Update text in session state if edited
                    if segment_text != segment_data['text']:
                        st.session_state['segment_audios'][i]['text'] = segment_text
                
                with col_audio:
                    # Generate a unique container for each audio to help with refresh
                    audio_container_id = f"audio_container_{i}"
                    if 'regenerate_timestamp' in st.session_state:
                        audio_container_id = f"audio_container_{i}_{st.session_state['regenerate_timestamp']}"
                    
                    # Use a container with key to force the UI to refresh
                    with st.container(key=audio_container_id):
                        # Use Streamlit's native audio component (without a key parameter)
                        audio_file = segment_data['file_path']
                        if os.path.exists(audio_file):
                            # Use the file path directly for better performance with large files
                            st.audio(audio_file, format="audio/wav")
                        else:
                            # Fall back to in-memory audio if file doesn't exist
                            try:
                                audio_buffer = io.BytesIO()
                                sf.write(audio_buffer, segment_data['audio'], 16000, format='WAV')
                                audio_buffer.seek(0)
                                st.audio(audio_buffer, format="audio/wav")
                            except Exception as e:
                                st.error(f"Error playing audio segment {i+1}: {str(e)}")
                
                with col_button:
                    regen_key = f"regen_btn_{i}"
                    if st.button("Regenerate", key=regen_key):
                        if 'tts_service' in st.session_state:
                            with st.spinner(f"Regenerating segment {i+1}..."):
                                try:
                                    # Re-generate audio for this segment using updated text
                                    current_text = st.session_state['segment_audios'][i]['text']
                                    
                                    # Prepare regeneration arguments
                                    regen_kwargs = {
                                        'prompt_speech_path': prompt_path,
                                    }
                                    
                                    # Add prompt_text for cosyvoice_api if this is the first regeneration
                                    if service_type == "cosyvoice_api" and prompt_text and not st.session_state['tts_service'].has_sent_prompt:
                                        regen_kwargs['prompt_text'] = prompt_text
                                    
                                    wav_data = st.session_state['tts_service'].generate_speech(
                                        text=current_text,
                                        **regen_kwargs
                                    )
                                    
                                    # Update segment audio in memory
                                    st.session_state['segment_audios'][i]['audio'] = wav_data
                                    
                                    # Format number with leading zeros
                                    padded_index = f"{i+1:03d}"
                                    
                                    # Save directly to cache if available (overriding original file)
                                    if 'cache_path' in st.session_state:
                                        cache_segment_path = os.path.join(
                                            st.session_state['cache_path'], 
                                            f"segment_{padded_index}.wav"
                                        )
                                        
                                        # Determine the sample rate based on service type or from existing audio
                                        if 'sample_rate' in st.session_state['segment_audios'][i]:
                                            # Use the sample rate from the original segment audio
                                            sample_rate = st.session_state['segment_audios'][i]['sample_rate']
                                        else:
                                            # Fallback to service-specific defaults
                                            sample_rate = 24000 if service_type.startswith("cosyvoice") else 16000
                                        
                                        # Save the audio to the cache file
                                        sf.write(cache_segment_path, wav_data, sample_rate)
                                        
                                        # Update the file path to point to the cache file
                                        st.session_state['segment_audios'][i]['file_path'] = cache_segment_path
                                        # Update the sample rate in case it was missing before
                                        st.session_state['segment_audios'][i]['sample_rate'] = sample_rate
                                        
                                        # Also update the text in the segments.txt file
                                        segments_path = os.path.join(st.session_state['cache_path'], "segments.txt")
                                        if os.path.exists(segments_path):
                                            with open(segments_path, "r", encoding="utf-8") as f:
                                                segments = f.read().split("\n\n")
                                            
                                            if i < len(segments):
                                                segments[i] = current_text
                                            
                                            with open(segments_path, "w", encoding="utf-8") as f:
                                                f.write("\n\n".join(segments))
                                    else:
                                        # If no cache exists yet, save to standard output (fallback only)
                                        output_path = f"output/segment_{padded_index}.wav"
                                        sf.write(output_path, wav_data, sample_rate)
                                        st.session_state['segment_audios'][i]['file_path'] = output_path
                                    
                                    # Add a timestamp to force refresh of audio elements
                                    st.session_state['regenerate_timestamp'] = int(time.time() * 1000)

                                    # Store the segment number that was regenerated in session state
                                    st.session_state['regenerated_segment_num'] = i + 1

                                    # Do rerun
                                    st.rerun()
                                except Exception as e:
                                    st.error(f"Error regenerating segment {i+1}: {str(e)}")
                    
                    # Display success message for this specific segment if it was just regenerated
                    if 'regenerated_segment_num' in st.session_state and st.session_state['regenerated_segment_num'] == i + 1:
                        st.success(f"Segment {i+1} regenerated!")
                        # Remove after displaying
                        del st.session_state['regenerated_segment_num']
        
        # Display merged audio if available
        if 'merged_audio' in st.session_state and st.session_state['merged_audio'] is not None:
            st.header("Complete Audio")
            
            # Use a container to force the UI to refresh
            with st.container():
                # Use Streamlit's native audio component for merged audio (without key parameter)
                if 'merged_audio_path' in st.session_state and os.path.exists(st.session_state['merged_audio_path']):
                    st.audio(st.session_state['merged_audio_path'], format="audio/wav")
                    st.success(f"Full audio saved to: {st.session_state['merged_audio_path']}")
                else:
                    try:
                        merged_buffer = io.BytesIO()
                        sf.write(merged_buffer, st.session_state['merged_audio'], 16000, format='WAV')
                        merged_buffer.seek(0)
                        st.audio(merged_buffer, format="audio/wav")
                    except Exception as e:
                        st.error(f"Error playing merged audio: {str(e)}")

        # Processing Monitoring Section
        if 'current_processor' in st.session_state and st.session_state.get('enable_parallel', True):
            processor = st.session_state['current_processor']

            with st.expander("Processing Logs & Monitoring", expanded=False):
                col1, col2 = st.columns(2)

                with col1:
                    st.subheader("Worker Status")
                    worker_status = processor.get_worker_status()

                    st.metric("Total Workers", worker_status['total_workers'])
                    st.metric("Recent Log Entries", worker_status['recent_log_count'])

                    if worker_status['worker_activity']:
                        st.write("**Worker Activity:**")
                        for worker_id, activity in worker_status['worker_activity'].items():
                            status_color = {
                                'processing': '🟡',
                                'completed_task': '🟢',
                                'failed_task': '🔴',
                                'unknown': '⚪'
                            }.get(activity['status'], '⚪')

                            st.write(f"{status_color} {worker_id}: {activity['status']} "
                                   f"(✅ {activity['tasks_completed']} ❌ {activity['tasks_failed']})")

                with col2:
                    st.subheader("Processing Logs")
                    logs = processor.get_logs()

                    if logs:
                        # Show last 10 logs
                        recent_logs = logs[-10:]
                        log_text = ""
                        for log in recent_logs:
                            timestamp = time.strftime('%H:%M:%S', time.localtime(log['timestamp']))
                            log_text += f"[{timestamp}] {log['level']}: {log['message']}\n"

                        st.text_area("Recent Logs", log_text, height=200, disabled=True)

                        if st.button("Clear Logs"):
                            processor.clear_logs()
                            st.rerun()
                    else:
                        st.info("No processing logs available")

        # Check if we need to remerge due to regeneration
        if 'needs_remerge' in st.session_state and st.session_state['needs_remerge']:
            st.session_state['needs_remerge'] = False
            # Trigger remerge
            wavs = []
            
            # Determine the sample rate from segment audio if available
            if len(st.session_state['segment_audios']) > 0 and 'sample_rate' in st.session_state['segment_audios'][0]:
                sample_rate = st.session_state['segment_audios'][0]['sample_rate']
            else:
                # Fallback to service-specific defaults
                service_type = st.session_state.get('service_type', 'spark')
                sample_rate = 24000 if service_type.startswith("cosyvoice") else 16000
            
            for i, segment_data in enumerate(st.session_state['segment_audios']):
                wavs.append(segment_data['audio'])
                
                # Add a silence gap after each segment except the last
                if i < len(st.session_state['segment_audios']) - 1:
                    if silence_duration == 1.0:
                        wavs.append(room_tone)
                    else:
                        silence_gap = np.zeros(int(sample_rate*silence_duration))
                        wavs.append(silence_gap)
            
            merged_audio = np.concatenate(wavs, axis=0)
            st.session_state['merged_audio'] = merged_audio
            
            # Save merged audio
            if 'merged_audio_path' in st.session_state:
                output_file = st.session_state['merged_audio_path']
                sf.write(output_file, merged_audio, sample_rate)
                print(f"Remerged audio saved at {sample_rate}Hz")

def increase_volume(audio, gain_db=5.0):
    gain_linear = 10 ** (gain_db / 20.0)  # Convert dB to linear scale
    return audio * gain_linear

def get_text_hash(text):
    """Generate a unique hash for the given text, ignoring newlines"""
    # Remove all newlines and whitespace for consistent hashing
    normalized_text = ' '.join(text.replace('\n', ' ').split())
    return hashlib.md5(normalized_text.encode('utf-8')).hexdigest()

def get_cache_dir_name(text):
    """Create a directory name based on first few characters and hash, with language detection for CJK texts"""
    # Remove newlines and excess spaces
    normalized_text = ' '.join(text.replace('\n', ' ').split())
    
    # Check if the text contains primarily Chinese/Japanese/Korean characters
    # We'll use a simple heuristic: if over 30% of the first 100 chars are CJK, treat as CJK text
    first_100_chars = normalized_text[:100]
    cjk_count = sum(1 for char in first_100_chars if ord(char) > 0x3000)  # Rough check for CJK Unicode range
    
    if len(first_100_chars) > 0 and cjk_count / len(first_100_chars) > 0.3:
        # For CJK text, use the first 10-15 characters directly
        first_chars = normalized_text[:15].replace(' ', '')
        prefix = first_chars
    else:
        # For Latin-based text, use first 5 words
        words = normalized_text.split()[:5]
        prefix = '_'.join(words)
    
    # Clean up directory name (remove special chars)
    prefix = ''.join(c if c.isalnum() or c == '_' else '_' for c in prefix)
    
    # Limit length
    if len(prefix) > 30:
        prefix = prefix[:30]
    
    # Get text hash
    text_hash = get_text_hash(text)[:8]  # Use first 8 chars of hash for brevity
    
    return f"{prefix}-{text_hash}"

def save_segments_to_cache(text, segments, segment_audios):
    """Save segments and audio files to a cache directory"""
    cache_dir_name = get_cache_dir_name(text)
    cache_path = os.path.join("output", cache_dir_name)
    
    # Create cache directory if it doesn't exist
    os.makedirs(cache_path, exist_ok=True)
    
    # Save original text
    with open(os.path.join(cache_path, "original_text.txt"), "w", encoding="utf-8") as f:
        f.write(text)
    
    # Save segments
    with open(os.path.join(cache_path, "segments.txt"), "w", encoding="utf-8") as f:
        f.write("\n\n".join(segments))
    
    # Save segment metadata
    metadata = {
        "timestamp": time.time(),
        "segment_count": len(segments),
        "text_hash": get_text_hash(text)
    }
    
    with open(os.path.join(cache_path, "metadata.json"), "w", encoding="utf-8") as f:
        json.dump(metadata, f)
    
    # Save audio files with zero-padded numbers
    for i, segment_data in enumerate(segment_audios):
        # Format number with leading zeros (e.g., 001, 002, etc.)
        padded_index = f"{i+1:03d}"
        output_path = os.path.join(cache_path, f"segment_{padded_index}.wav")
        sf.write(output_path, segment_data["audio"], 16000)
    
    return cache_path

def find_cache_for_text(text):
    """Find a cache directory for the given text by hash"""
    text_hash = get_text_hash(text)
    print("+++++++++++Searching for cache for text:", text_hash)
    base_output_dir = "output"
    
    # Search through all directories in output folder
    for item in os.listdir(base_output_dir):
        item_path = os.path.join(base_output_dir, item)
        if os.path.isdir(item_path) and "-" in item:
            # Check if metadata.json exists
            metadata_path = os.path.join(item_path, "metadata.json")
            if os.path.exists(metadata_path):
                try:
                    with open(metadata_path, "r", encoding="utf-8") as f:
                        metadata = json.load(f)
                    
                    if metadata.get("text_hash") == text_hash:
                        return item_path
                except:
                    continue
    
    return None

def load_segments_from_cache(cache_path):
    """Load segments and audio files from a cache directory"""
    segments = []
    segment_audios = []
    
    # Load segments
    segments_path = os.path.join(cache_path, "segments.txt")
    if os.path.exists(segments_path):
        with open(segments_path, "r", encoding="utf-8") as f:
            segments_text = f.read()
        segments = [seg.strip() for seg in segments_text.split("\n\n") if seg.strip()]
    
    # Load audio files with proper numeric sorting
    segment_files = glob.glob(os.path.join(cache_path, "segment_*.wav"))
    
    # Custom sort function that extracts the numeric part for sorting
    def extract_number(filename):
        # Extract the number between "segment_" and ".wav"
        try:
            return int(os.path.basename(filename).replace("segment_", "").replace(".wav", ""))
        except ValueError:
            return 0
    
    # Sort files numerically based on segment index
    segment_files.sort(key=extract_number)
    
    for i, segment_file in enumerate(segment_files):
        if i < len(segments):
            try:
                # Read audio data from file
                audio_data, sample_rate = sf.read(segment_file)
                
                # Use the original file path from cache - don't copy to output directory
                segment_audios.append({
                    'index': i,
                    'text': segments[i],
                    'audio': audio_data,
                    'file_path': segment_file,  # Use file directly from cache
                    'sample_rate': sample_rate  # Store actual sample rate
                })
            except Exception as e:
                print(f"Error loading segment {i}: {str(e)}")
    
    return segments, segment_audios

def list_available_caches():
    """List all available cache directories with their first line of text"""
    base_output_dir = "output"
    caches = []
    
    for item in os.listdir(base_output_dir):
        item_path = os.path.join(base_output_dir, item)
        if os.path.isdir(item_path) and "-" in item:
            # Check if metadata.json exists
            metadata_path = os.path.join(item_path, "metadata.json")
            original_text_path = os.path.join(item_path, "original_text.txt")
            
            if os.path.exists(metadata_path) and os.path.exists(original_text_path):
                try:
                    with open(metadata_path, "r", encoding="utf-8") as f:
                        metadata = json.load(f)
                    
                    with open(original_text_path, "r", encoding="utf-8") as f:
                        original_text = f.read()
                        first_line = original_text.split("\n")[0][:50]
                    
                    caches.append({
                        "path": item_path,
                        "name": item,
                        "first_line": first_line,
                        "timestamp": metadata.get("timestamp", 0),
                        "segment_count": metadata.get("segment_count", 0)
                    })
                except:
                    continue
    
    # Sort by timestamp (newest first)
    caches.sort(key=lambda x: x["timestamp"], reverse=True)
    return caches

def detect_trailing_silence(audio, sample_rate, threshold=0.001, min_silence_ms=300):
    min_silence_samples = int(sample_rate * (min_silence_ms / 1000.0))
    tail = audio[-min_silence_samples:]
    
    # Check if mean absolute volume is below threshold
    if np.mean(np.abs(tail)) < threshold:
        return True
    return False

def create_soft_gap(prev_audio, sample_rate, gap_duration=1.0):
    # Fade the last 0.3 seconds of previous audio
    fade_duration = min(0.3, gap_duration)
    fade_samples = int(fade_duration * sample_rate)
    gap_samples = int(gap_duration * sample_rate)

    # Get the end of the audio to fade
    fade_out = prev_audio[-fade_samples:] * np.linspace(1, 0, fade_samples)

    # Fill the rest of the gap with low-level noise
    remaining_gap = gap_samples - fade_samples
    ambient_noise = np.random.normal(0, 0.001, remaining_gap)  # Very soft noise

    return np.concatenate([fade_out, ambient_noise])

if __name__ == "__main__":
    main()