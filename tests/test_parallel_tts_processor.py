import unittest
import time
import numpy as np
from unittest.mock import Mock, MagicMock
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.parallel_tts_processor import ParallelTTSProcessor, TTSTask, TTSResult

class MockTTSService:
    """Mock TTS service for testing"""
    
    def __init__(self, processing_time=0.1, failure_rate=0.0):
        self.processing_time = processing_time
        self.failure_rate = failure_rate
        self.call_count = 0
        
    def generate_speech(self, text, **kwargs):
        """Mock speech generation"""
        self.call_count += 1
        
        # Simulate processing time
        time.sleep(self.processing_time)
        
        # Simulate failures based on failure rate
        if self.failure_rate > 0 and (self.call_count * self.failure_rate) >= 1:
            if self.call_count % int(1 / self.failure_rate) == 0:
                raise Exception(f"Mock failure for text: {text[:20]}...")
        
        # Return mock audio data
        return np.random.random(1000).astype(np.float32)

class TestParallelTTSProcessor(unittest.TestCase):
    """Test cases for ParallelTTSProcessor"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_tts_service = MockTTSService()
        self.test_segments = [
            "This is the first test segment.",
            "This is the second test segment.",
            "This is the third test segment.",
            "This is the fourth test segment.",
            "This is the fifth test segment."
        ]
        
    def test_basic_parallel_processing(self):
        """Test basic parallel processing functionality"""
        processor = ParallelTTSProcessor(
            tts_service=self.mock_tts_service,
            num_workers=2,
            enable_logging=False
        )
        
        results = processor.process_segments(self.test_segments)
        
        # Check that we got results for all segments
        self.assertEqual(len(results), len(self.test_segments))
        
        # Check that results are in correct order
        for i, result in enumerate(results):
            self.assertEqual(result.index, i)
            self.assertEqual(result.text, self.test_segments[i])
            self.assertTrue(result.success)
            self.assertIsInstance(result.audio_data, np.ndarray)
            self.assertIsNotNone(result.worker_id)
            self.assertIsNotNone(result.processing_time)
    
    def test_queue_based_processing(self):
        """Test queue-based processing"""
        processor = ParallelTTSProcessor(
            tts_service=self.mock_tts_service,
            num_workers=2,
            enable_logging=False
        )
        
        results = processor.process_segments_queue_based(self.test_segments)
        
        # Check that we got results for all segments
        self.assertEqual(len(results), len(self.test_segments))
        
        # Check that results are in correct order
        for i, result in enumerate(results):
            self.assertEqual(result.index, i)
            self.assertTrue(result.success)
    
    def test_error_handling(self):
        """Test error handling with failing TTS service"""
        failing_service = MockTTSService(failure_rate=0.4)  # 40% failure rate
        
        processor = ParallelTTSProcessor(
            tts_service=failing_service,
            num_workers=2,
            enable_logging=False
        )
        
        results = processor.process_segments(self.test_segments)
        
        # Check that we got results for all segments
        self.assertEqual(len(results), len(self.test_segments))
        
        # Check that some failed and some succeeded
        successful_results = [r for r in results if r.success]
        failed_results = [r for r in results if not r.success]
        
        self.assertGreater(len(failed_results), 0, "Expected some failures")
        self.assertGreater(len(successful_results), 0, "Expected some successes")
        
        # Check failed results have error messages
        for result in failed_results:
            self.assertIsNotNone(result.error)
            self.assertEqual(len(result.audio_data), 0)
    
    def test_retry_functionality(self):
        """Test retry functionality for failed tasks"""
        # Create a service that fails initially but succeeds on retry
        class RetryMockService:
            def __init__(self):
                self.attempt_count = {}
                
            def generate_speech(self, text, **kwargs):
                if text not in self.attempt_count:
                    self.attempt_count[text] = 0
                self.attempt_count[text] += 1
                
                # Fail on first attempt, succeed on second
                if self.attempt_count[text] == 1:
                    raise Exception(f"First attempt failure for: {text[:20]}...")
                
                time.sleep(0.05)  # Simulate processing time
                return np.random.random(1000).astype(np.float32)
        
        retry_service = RetryMockService()
        processor = ParallelTTSProcessor(
            tts_service=retry_service,
            num_workers=2,
            enable_logging=False
        )
        
        results = processor.process_segments_with_retry(self.test_segments, max_retries=2)
        
        # All should succeed after retry
        successful_results = [r for r in results if r.success]
        self.assertEqual(len(successful_results), len(self.test_segments))
    
    def test_performance_improvement(self):
        """Test that parallel processing is faster than sequential"""
        # Use longer processing time to make the difference more apparent
        slow_service = MockTTSService(processing_time=0.2)
        
        # Sequential processing (1 worker)
        sequential_processor = ParallelTTSProcessor(
            tts_service=slow_service,
            num_workers=1,
            enable_logging=False
        )
        
        start_time = time.time()
        sequential_results = sequential_processor.process_segments(self.test_segments[:3])
        sequential_time = time.time() - start_time
        
        # Reset service call count
        slow_service.call_count = 0
        
        # Parallel processing (2 workers)
        parallel_processor = ParallelTTSProcessor(
            tts_service=slow_service,
            num_workers=2,
            enable_logging=False
        )
        
        start_time = time.time()
        parallel_results = parallel_processor.process_segments(self.test_segments[:3])
        parallel_time = time.time() - start_time
        
        # Parallel should be faster (with some tolerance for overhead)
        self.assertLess(parallel_time, sequential_time * 0.8, 
                       f"Parallel time ({parallel_time:.2f}s) should be less than sequential time ({sequential_time:.2f}s)")
        
        # Both should produce the same number of successful results
        self.assertEqual(len(sequential_results), len(parallel_results))
    
    def test_progress_callback(self):
        """Test progress callback functionality"""
        progress_updates = []
        
        def progress_callback(completed, total):
            progress_updates.append((completed, total))
        
        processor = ParallelTTSProcessor(
            tts_service=self.mock_tts_service,
            num_workers=2,
            progress_callback=progress_callback,
            enable_logging=False
        )
        
        results = processor.process_segments(self.test_segments)
        
        # Check that progress was reported
        self.assertGreater(len(progress_updates), 0)
        
        # Check that final progress shows completion
        final_progress = progress_updates[-1]
        self.assertEqual(final_progress[0], len(self.test_segments))
        self.assertEqual(final_progress[1], len(self.test_segments))
    
    def test_processing_stats(self):
        """Test processing statistics generation"""
        processor = ParallelTTSProcessor(
            tts_service=self.mock_tts_service,
            num_workers=2,
            enable_logging=False
        )
        
        results = processor.process_segments(self.test_segments)
        stats = processor.get_processing_stats(results)
        
        # Check stats structure
        self.assertIn('total_tasks', stats)
        self.assertIn('successful_tasks', stats)
        self.assertIn('failed_tasks', stats)
        self.assertIn('success_rate', stats)
        self.assertIn('total_processing_time', stats)
        self.assertIn('average_processing_time', stats)
        self.assertIn('worker_distribution', stats)
        
        # Check stats values
        self.assertEqual(stats['total_tasks'], len(self.test_segments))
        self.assertEqual(stats['successful_tasks'], len(self.test_segments))
        self.assertEqual(stats['failed_tasks'], 0)
        self.assertEqual(stats['success_rate'], 1.0)
        self.assertGreater(stats['total_processing_time'], 0)
        self.assertGreater(stats['average_processing_time'], 0)

if __name__ == '__main__':
    unittest.main()
