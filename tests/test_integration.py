import unittest
import tempfile
import os
import sys
import numpy as np
from unittest.mock import Mock, patch

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.parallel_tts_processor import ParallelTTSProcessor
from services.tts_service import TTSService

class MockTTSService(TTSService):
    """Mock TTS service that implements the TTSService interface"""
    
    def __init__(self, device="cpu"):
        self.device = device
        self.call_count = 0
        
    def initialize(self, device="cpu"):
        self.device = device
        
    def generate_speech(self, text, **kwargs):
        """Mock speech generation that returns realistic audio data"""
        self.call_count += 1
        
        # Simulate different processing times based on text length
        import time
        processing_time = len(text) * 0.001  # 1ms per character
        time.sleep(processing_time)
        
        # Return mock audio data with realistic shape
        sample_rate = 16000
        duration = max(0.5, len(text) * 0.05)  # Minimum 0.5s, 50ms per character
        num_samples = int(sample_rate * duration)
        
        return np.random.random(num_samples).astype(np.float32)

class TestIntegration(unittest.TestCase):
    """Integration tests for parallel processing with realistic scenarios"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_service = MockTTSService()
        
        # Realistic text segments of varying lengths
        self.realistic_segments = [
            "Hello, this is a short segment.",
            "This is a medium-length segment that contains a bit more text to process and should take slightly longer to generate.",
            "Short one.",
            "This is another longer segment with multiple sentences. It should demonstrate how the parallel processor handles varying workloads. The processing time will be proportional to the text length.",
            "Final segment here.",
            "This segment has some special characters: punctuation! Questions? And numbers like 123.",
            "A segment with different formatting and    extra    spaces.",
            "Last but not least, this segment tests the system's ability to handle the final chunk in a batch."
        ]
    
    def test_realistic_parallel_processing(self):
        """Test parallel processing with realistic text segments"""
        processor = ParallelTTSProcessor(
            tts_service=self.mock_service,
            num_workers=3,
            enable_logging=True
        )
        
        results = processor.process_segments(self.realistic_segments)
        
        # Verify all segments were processed
        self.assertEqual(len(results), len(self.realistic_segments))
        
        # Verify order is maintained
        for i, result in enumerate(results):
            self.assertEqual(result.index, i)
            self.assertEqual(result.text, self.realistic_segments[i])
            self.assertTrue(result.success)
            
        # Verify audio data is realistic
        for result in results:
            self.assertIsInstance(result.audio_data, np.ndarray)
            self.assertGreater(len(result.audio_data), 1000)  # At least 1000 samples
            
        # Verify processing stats
        stats = processor.get_processing_stats(results)
        self.assertEqual(stats['success_rate'], 1.0)
        self.assertEqual(stats['total_tasks'], len(self.realistic_segments))
        self.assertGreater(stats['total_processing_time'], 0)
        
        # Verify worker distribution (should use multiple workers)
        self.assertGreater(len(stats['worker_distribution']), 1)
    
    def test_queue_vs_threadpool_comparison(self):
        """Compare queue-based and threadpool-based processing"""
        # Test with threadpool
        processor_threadpool = ParallelTTSProcessor(
            tts_service=self.mock_service,
            num_workers=2,
            enable_logging=False
        )
        
        import time
        start_time = time.time()
        results_threadpool = processor_threadpool.process_segments(self.realistic_segments[:4])
        threadpool_time = time.time() - start_time
        
        # Reset service call count
        self.mock_service.call_count = 0
        
        # Test with queue
        processor_queue = ParallelTTSProcessor(
            tts_service=self.mock_service,
            num_workers=2,
            enable_logging=False
        )
        
        start_time = time.time()
        results_queue = processor_queue.process_segments_queue_based(self.realistic_segments[:4])
        queue_time = time.time() - start_time
        
        # Both should produce the same results
        self.assertEqual(len(results_threadpool), len(results_queue))
        
        # Both should be successful
        for results in [results_threadpool, results_queue]:
            for result in results:
                self.assertTrue(result.success)
        
        # Times should be comparable (within 50% of each other)
        time_ratio = max(threadpool_time, queue_time) / min(threadpool_time, queue_time)
        self.assertLess(time_ratio, 1.5, f"Processing times too different: threadpool={threadpool_time:.2f}s, queue={queue_time:.2f}s")
    
    def test_error_recovery_integration(self):
        """Test error recovery in realistic scenarios"""
        # Create a service that fails on specific text patterns
        class SelectiveFailureService(TTSService):
            def __init__(self):
                self.call_count = 0
                
            def initialize(self, device="cpu"):
                pass
                
            def generate_speech(self, text, **kwargs):
                self.call_count += 1
                
                # Fail on segments containing "special characters"
                if "special characters" in text:
                    raise Exception("Failed to process special characters")
                
                # Simulate processing time
                import time
                time.sleep(0.05)
                
                return np.random.random(1000).astype(np.float32)
        
        failing_service = SelectiveFailureService()
        processor = ParallelTTSProcessor(
            tts_service=failing_service,
            num_workers=2,
            enable_logging=False
        )
        
        results = processor.process_segments(self.realistic_segments)
        
        # Check that we have both successful and failed results
        successful_results = [r for r in results if r.success]
        failed_results = [r for r in results if not r.success]
        
        self.assertGreater(len(successful_results), 0)
        self.assertGreater(len(failed_results), 0)
        
        # Check that the failed result is the one with special characters
        failed_texts = [r.text for r in failed_results]
        self.assertTrue(any("special characters" in text for text in failed_texts))
        
        # Check that failed results have proper error information
        for result in failed_results:
            self.assertIsNotNone(result.error)
            self.assertIn("special characters", result.error)
    
    def test_progress_tracking_integration(self):
        """Test progress tracking with realistic workload"""
        progress_updates = []
        
        def progress_callback(completed, total):
            progress_updates.append((completed, total, time.time()))
        
        processor = ParallelTTSProcessor(
            tts_service=self.mock_service,
            num_workers=2,
            progress_callback=progress_callback,
            enable_logging=False
        )
        
        import time
        start_time = time.time()
        results = processor.process_segments(self.realistic_segments)
        end_time = time.time()
        
        # Verify progress tracking
        self.assertGreater(len(progress_updates), 0)
        
        # Check that progress is monotonically increasing
        for i in range(1, len(progress_updates)):
            self.assertGreaterEqual(progress_updates[i][0], progress_updates[i-1][0])
        
        # Check that final progress shows completion
        final_progress = progress_updates[-1]
        self.assertEqual(final_progress[0], len(self.realistic_segments))
        self.assertEqual(final_progress[1], len(self.realistic_segments))
        
        # Check that progress timestamps are within the processing timeframe
        for completed, total, timestamp in progress_updates:
            self.assertGreaterEqual(timestamp, start_time)
            self.assertLessEqual(timestamp, end_time)
    
    def test_logging_integration(self):
        """Test logging functionality with realistic processing"""
        processor = ParallelTTSProcessor(
            tts_service=self.mock_service,
            num_workers=2,
            enable_logging=True
        )
        
        # Clear any existing logs
        processor.clear_logs()
        
        results = processor.process_segments(self.realistic_segments[:3])
        
        # Get logs
        logs = processor.get_logs()
        
        # Should have logs
        self.assertGreater(len(logs), 0)
        
        # Should have logs from different workers
        worker_ids = set()
        for log in logs:
            if 'Worker' in log['message']:
                # Extract worker ID from message
                parts = log['message'].split()
                for i, part in enumerate(parts):
                    if part == 'Worker' and i + 1 < len(parts):
                        worker_ids.add(parts[i + 1])
        
        self.assertGreater(len(worker_ids), 1, "Should have logs from multiple workers")
        
        # Get worker status
        worker_status = processor.get_worker_status()
        self.assertEqual(worker_status['total_workers'], 2)
        self.assertGreater(worker_status['recent_log_count'], 0)

if __name__ == '__main__':
    unittest.main()
